<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
  
    
    <!-- Primary Meta Tags -->
    <title>Skincare Compass - The Ultimate Ingredient Encyclopedia & Guide</title>
    <meta name="title" content="Skincare Compass - The Ultimate Ingredient Encyclopedia & Guide" />
    <meta name="description" content="The world's most trusted resource for skincare ingredient information, combination guides, and beginner-friendly education. Discover 2,500+ ingredients, expert combinations, and science-backed advice." />
    <meta name="keywords" content="skincare ingredients, skincare routine, ingredient compatibility, skincare guide, skincare science, dermatology, cosmetic chemistry, skincare education" />
    <meta name="author" content="Skincare Compass" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.skincarecompass.com/" />
    <meta property="og:title" content="Skincare Compass - The Ultimate Ingredient Encyclopedia" />
    <meta property="og:description" content="The world's most trusted resource for skincare ingredient information, combination guides, and beginner-friendly education." />
    <meta property="og:image" content="https://www.skincarecompass.com/og-image.jpg" />
    <meta property="og:site_name" content="Skincare Compass" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://www.skincarecompass.com/" />
    <meta property="twitter:title" content="Skincare Compass - The Ultimate Ingredient Encyclopedia" />
    <meta property="twitter:description" content="The world's most trusted resource for skincare ingredient information, combination guides, and beginner-friendly education." />
    <meta property="twitter:image" content="https://www.skincarecompass.com/twitter-image.jpg" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.skincarecompass.com/" />
    
    <!-- Hreflang for International SEO -->
    <link rel="alternate" hreflang="en-us" href="https://www.skincarecompass.com/" />
    <link rel="alternate" hreflang="en-gb" href="https://www.skincarecompass.com/" />
    <link rel="alternate" hreflang="en-ca" href="https://www.skincarecompass.com/" />
    <link rel="alternate" hreflang="en-au" href="https://www.skincarecompass.com/" />
    <link rel="alternate" hreflang="x-default" href="https://www.skincarecompass.com/" />
    
    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Structured Data - Organization -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Skincare Compass",
      "url": "https://www.skincarecompass.com",
      "logo": "https://www.skincarecompass.com/logo.png",
      "description": "The world's most trusted resource for skincare ingredient information and education",
      "foundingDate": "2023",
      "founders": [
        {
          "@type": "Person",
          "name": "Dr. Sarah Chen",
          "jobTitle": "Chief Medical Officer"
        }
      ],
      "sameAs": [
        "https://twitter.com/skincarecompass",
        "https://instagram.com/skincarecompass",
        "https://youtube.com/skincarecompass"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-123-4567",
        "contactType": "customer service",
        "email": "<EMAIL>"
      }
    }
    </script>
    
    <!-- Structured Data - Website -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Skincare Compass",
      "url": "https://www.skincarecompass.com",
      "description": "The ultimate resource for skincare ingredient information, combination guides, and beginner-friendly education",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://www.skincarecompass.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
