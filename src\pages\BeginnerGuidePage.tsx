import React from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Clock, BookOpen, CheckCircle, AlertTriangle, Star, Users, BarChart3, Lightbulb } from 'lucide-react';
import { beginnerGuides } from '../data/beginnerGuides';
import MetaTags from '../components/SEO/MetaTags';
import IngredientCompatibilityMatrix from '../components/infographics/IngredientCompatibilityMatrix';
import SkinTypeDecisionTree from '../components/infographics/SkinTypeDecisionTree';

const BeginnerGuidePage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();

  // Get guide data from our data file
  const guide = slug ? beginnerGuides[slug] : null;

  if (!guide) {
    return (
      <div className="pt-16 min-h-screen bg-brand-off-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-brand-charcoal mb-4">Guide Not Found</h1>
          <Link to="/beginners" className="btn-primary">
            Browse All Guides
          </Link>
        </div>
      </div>
    );
  }

  // SEO Data
  const seoTitle = `${guide.title} - Complete Beginner's Guide`;
  const seoDescription = guide.description;
  const seoKeywords = `skincare beginner guide, ${guide.title.toLowerCase()}, skincare routine, skincare basics, beginner skincare, skincare education`;
  const canonicalUrl = `https://www.skincarecompass.com/beginners/${slug}/`;

  // Structured Data for Article
  const articleStructuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": guide.title,
    "description": guide.description,
    "image": `https://www.skincarecompass.com/images/guides/${slug}.jpg`,
    "author": {
      "@type": "Person",
      "name": guide.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "Skincare Compass",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.skincarecompass.com/logo.png"
      }
    },
    "datePublished": "2024-12-15",
    "dateModified": guide.lastUpdated,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    },
    "wordCount": guide.sections.reduce((total, section) => total + section.content.length, 0),
    "timeRequired": guide.readTime
  };

  // HowTo Structured Data for guides with steps
  const howToStructuredData = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": guide.title,
    "description": guide.description,
    "image": `https://www.skincarecompass.com/images/guides/${slug}.jpg`,
    "totalTime": guide.readTime,
    "step": guide.sections.map((section, index) => ({
      "@type": "HowToStep",
      "name": section.title,
      "text": section.content.substring(0, 500) + "...",
      "position": index + 1
    }))
  };

  // Render appropriate infographic based on guide slug
  const renderInfographic = () => {
    switch (slug) {
      case 'skincare-101':
        return (
          <div className="mb-12">
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                  <BarChart3 className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  Essential Skincare Routine Flowchart
                </h3>
                <p className="text-gray-600">
                  Visual guide to building your first skincare routine
                </p>
              </div>

              <div className="max-w-2xl mx-auto space-y-4">
                <div className="bg-blue-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-blue-800 mb-2">Step 1: Cleanse</h4>
                  <p className="text-sm text-blue-700">🧼 Remove dirt, oil, and impurities</p>
                  <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-white rounded p-2 text-blue-600">
                      <span className="font-medium">Dry Skin:</span> Cream cleanser
                    </div>
                    <div className="bg-white rounded p-2 text-blue-600">
                      <span className="font-medium">Oily Skin:</span> Gel cleanser
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-green-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-green-800 mb-2">Step 2: Treat (Optional)</h4>
                  <p className="text-sm text-green-700">✨ Target specific skin concerns</p>
                  <div className="mt-2 grid grid-cols-3 gap-2 text-xs">
                    <div className="bg-white rounded p-2 text-green-600">
                      <span className="font-medium">Acne:</span> Salicylic Acid
                    </div>
                    <div className="bg-white rounded p-2 text-green-600">
                      <span className="font-medium">Dryness:</span> Hyaluronic Acid
                    </div>
                    <div className="bg-white rounded p-2 text-green-600">
                      <span className="font-medium">Aging:</span> Retinol (PM)
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-purple-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-purple-800 mb-2">Step 3: Moisturize</h4>
                  <p className="text-sm text-purple-700">🧴 Hydrate and seal in moisture</p>
                  <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-white rounded p-2 text-purple-600">
                      <span className="font-medium">Dry Skin:</span> Rich cream
                    </div>
                    <div className="bg-white rounded p-2 text-purple-600">
                      <span className="font-medium">Oily Skin:</span> Gel moisturizer
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="w-px h-8 bg-gray-300"></div>
                </div>
                
                <div className="bg-orange-50 rounded-lg p-4 text-center">
                  <h4 className="font-bold text-orange-800 mb-2">Step 4: Protect (AM Only)</h4>
                  <p className="text-sm text-orange-700">☀️ SPF 30+ sunscreen daily</p>
                  <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                    <div className="bg-white rounded p-2 text-orange-600">
                      <span className="font-medium">Sensitive:</span> Mineral SPF
                    </div>
                    <div className="bg-white rounded p-2 text-orange-600">
                      <span className="font-medium">Oily:</span> Oil-free SPF
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'ingredient-dictionary':
        return <IngredientCompatibilityMatrix />;
      
      case 'how-to-read-labels':
        return (
          <div className="mb-12">
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
                  <Lightbulb className="w-8 h-8 text-brand-teal" />
                </div>
                <h3 className="text-2xl font-bold text-brand-charcoal mb-2">
                  How to Decode Skincare Labels
                </h3>
                <p className="text-gray-600">
                  Visual guide to understanding ingredient lists and marketing claims
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h4 className="font-bold text-brand-charcoal text-lg">Ingredient List Anatomy</h4>
                  
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xs">1</div>
                      <span className="font-medium text-blue-800">First 5 Ingredients</span>
                    </div>
                    <p className="text-sm text-blue-700">Make up 80% of the formula. Most important!</p>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xs">2</div>
                      <span className="font-medium text-purple-800">Middle Ingredients</span>
                    </div>
                    <p className="text-sm text-purple-700">Present in moderate amounts (1-5%)</p>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-xs">3</div>
                      <span className="font-medium text-green-800">Last Ingredients</span>
                    </div>
                    <p className="text-sm text-green-700">Less than 1% - often preservatives, fragrance</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-bold text-brand-charcoal text-lg">Marketing Claims Decoder</h4>
                  
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <h5 className="font-medium text-green-800 mb-2">Regulated Claims</h5>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>• "SPF 30" - Tested and verified</li>
                      <li>• "Hypoallergenic" - Reduced allergen potential</li>
                      <li>• "Non-comedogenic" - Won't clog pores</li>
                    </ul>
                  </div>
                  
                  <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                    <h5 className="font-medium text-yellow-800 mb-2">Questionable Claims</h5>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• "Natural" - No legal definition</li>
                      <li>• "Dermatologist tested" - By whom? How many?</li>
                      <li>• "Clean" - Brand-defined term</li>
                    </ul>
                  </div>
                  
                  <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                    <h5 className="font-medium text-red-800 mb-2">Red Flag Claims</h5>
                    <ul className="text-sm text-red-700 space-y-1">
                      <li>• "Chemical-free" - Everything is chemicals</li>
                      <li>• "Instant results" - Unrealistic timeframe</li>
                      <li>• "Miracle cure" - Overpromising</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'skin-types-guide':
        return <SkinTypeDecisionTree />;
      
      default:
        return null;
    }
  };

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        canonicalUrl={canonicalUrl}
        structuredData={[articleStructuredData, howToStructuredData]}
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-4">
          <nav aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              <li>
                <Link to="/" className="text-gray-500 hover:text-brand-teal">Home</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li>
                <Link to="/beginners" className="text-gray-500 hover:text-brand-teal">Beginner's Hub</Link>
              </li>
              <li className="text-gray-400">/</li>
              <li className="text-brand-charcoal font-medium">{guide.title}</li>
            </ol>
          </nav>
          <Link
            to="/beginners"
            className="inline-flex items-center space-x-2 text-brand-teal hover:text-brand-teal-dark transition-colors duration-200 mt-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Beginner's Hub</span>
          </Link>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white">
        <div className="container-custom py-12">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <div className="flex items-center space-x-4 mb-4">
                <span className="text-sm bg-category-green text-white px-3 py-1 rounded-full">
                  {guide.difficulty}
                </span>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{guide.readTime}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{guide.readers} readers</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span>{guide.rating}/5</span>
                  </div>
                </div>
              </div>

              <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
                {guide.title}
              </h1>

              <p className="text-xl text-gray-600 leading-relaxed mb-6">
                {guide.description}
              </p>

              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <span>By {guide.author}</span>
                <span>•</span>
                <span>Updated {guide.lastUpdated}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg max-w-none">
              {guide.sections.map((section, index) => (
                <div key={index} className="mb-12">
                  <h2 className="text-2xl font-bold text-brand-charcoal mb-6">
                    {section.title}
                  </h2>
                  <div className="text-gray-600 leading-relaxed whitespace-pre-line">
                    {section.content}
                  </div>
                </div>
              ))}
            </div>

            {/* Infographic Section */}
            {renderInfographic()}
          </div>
        </div>
      </div>

      {/* Tips & Mistakes */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Pro Tips */}
              <div className="card p-6 border-2 border-green-200 bg-green-50">
                <div className="flex items-center space-x-2 mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <h3 className="text-lg font-semibold text-green-800">Pro Tips</h3>
                </div>
                <ul className="space-y-3">
                  {guide.tips.map((tip, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-green-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-green-700 text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Common Mistakes */}
              <div className="card p-6 border-2 border-red-200 bg-red-50">
                <div className="flex items-center space-x-2 mb-4">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                  <h3 className="text-lg font-semibold text-red-800">Avoid These Mistakes</h3>
                </div>
                <ul className="space-y-3">
                  {guide.commonMistakes.map((mistake, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-red-700 text-sm">{mistake}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
              Continue Your Learning Journey
            </h2>
            
            <div className="grid md:grid-cols-3 gap-6">
              {guide.nextSteps.map((step, index) => (
                <Link
                  key={index}
                  to={`/beginners/${step.slug}`}
                  className="card p-6 hover:scale-105 transition-all duration-200 group"
                  aria-label={`Continue to ${step.title}`}
                >
                  <div className="flex items-center space-x-2 mb-3">
                    <BookOpen className="w-5 h-5 text-brand-teal" />
                    <span className="text-sm text-gray-500">Next Guide</span>
                  </div>
                  <h3 className="text-lg font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {step.description}
                  </p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA */}
      <div className="section-padding bg-brand-off-white">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-brand-teal/10 to-brand-teal-light/10 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-brand-charcoal mb-4">
                Ready to Build Your Routine?
              </h3>
              <p className="text-gray-600 mb-6">
                Take our personalized quiz to get product recommendations tailored to your skin type and concerns.
              </p>
              <Link to="/tools/routine-builder-quiz" className="btn-primary">
                Take Routine Quiz
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BeginnerGuidePage;