import React from 'react';
import { Layers, ArrowDown, Droplets, Zap, Shield, Sparkles } from 'lucide-react';

const IngredientPenetrationChart: React.FC = () => {
  const skinLayers = [
    {
      name: 'Stratum Corneum',
      description: 'Outermost protective layer',
      depth: '0.01-0.02mm',
      color: 'bg-amber-100',
      borderColor: 'border-amber-200',
      ingredients: [
        { name: 'High MW Hyaluronic Acid', icon: Droplets, color: 'bg-blue-500', size: '1,000-1,800 kDa' },
        { name: 'Mineral Sunscreens', icon: Shield, color: 'bg-yellow-500', size: 'Particulate' },
        { name: 'Silicones', icon: Shield, color: 'bg-gray-500', size: 'Film-forming' },
        { name: 'Ceramides', icon: Shield, color: 'bg-green-500', size: '500-1,000 Da' },
      ],
    },
    {
      name: 'Epidermis',
      description: 'Living skin cells, no blood vessels',
      depth: '0.05-0.1mm',
      color: 'bg-orange-100',
      borderColor: 'border-orange-200',
      ingredients: [
        { name: 'Medium MW Hyaluronic Acid', icon: Droplets, color: 'bg-blue-500', size: '100-1,000 kDa' },
        { name: 'Niacinamide', icon: Zap, color: 'bg-purple-500', size: '122 Da' },
        { name: 'Vitamin C Derivatives', icon: Sparkles, color: 'bg-orange-500', size: '200-500 Da' },
        { name: 'AHAs (Glycolic Acid)', icon: Zap, color: 'bg-pink-500', size: '76 Da' },
      ],
    },
    {
      name: 'Dermis',
      description: 'Collagen, elastin, blood vessels',
      depth: '0.5-3mm',
      color: 'bg-red-100',
      borderColor: 'border-red-200',
      ingredients: [
        { name: 'Low MW Hyaluronic Acid', icon: Droplets, color: 'bg-blue-500', size: '10-100 kDa' },
        { name: 'Retinol', icon: Sparkles, color: 'bg-purple-500', size: '286 Da' },
        { name: 'Peptides', icon: Zap, color: 'bg-teal-500', size: '500-1,500 Da' },
        { name: 'L-Ascorbic Acid', icon: Sparkles, color: 'bg-orange-500', size: '176 Da' },
      ],
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <Layers className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Ingredient Penetration Depths
        </h2>
        <p className="text-gray-600">
          Understanding how deep skincare ingredients can reach
        </p>
      </div>

      <div className="space-y-6 max-w-4xl mx-auto">
        {skinLayers.map((layer, index) => (
          <div key={index}>
            <div className={`rounded-xl ${layer.color} border ${layer.borderColor} p-6`}>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-bold text-brand-charcoal">{layer.name}</h3>
                  <p className="text-sm text-gray-600">{layer.description}</p>
                </div>
                <div className="mt-2 md:mt-0">
                  <span className="text-sm bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full text-gray-700">
                    Depth: {layer.depth}
                  </span>
                </div>
              </div>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                {layer.ingredients.map((ingredient, idx) => (
                  <div key={idx} className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`w-8 h-8 ${ingredient.color} rounded-full flex items-center justify-center text-white`}>
                        <ingredient.icon className="w-4 h-4" />
                      </div>
                      <h4 className="font-semibold text-brand-charcoal text-sm">{ingredient.name}</h4>
                    </div>
                    <div className="text-xs text-gray-500">
                      Molecular Size: <span className="font-medium text-gray-700">{ingredient.size}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {index < skinLayers.length - 1 && (
              <div className="flex justify-center my-2">
                <ArrowDown className="w-6 h-6 text-gray-400" />
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold text-brand-charcoal mb-3">Factors Affecting Penetration</h3>
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <h4 className="font-medium text-brand-charcoal mb-2">Molecular Weight</h4>
            <p className="text-xs text-gray-600">
              Smaller molecules (under 500 Da) penetrate deeper. The 500 Da rule states that molecules larger than this rarely penetrate beyond the stratum corneum.
            </p>
          </div>
          
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <h4 className="font-medium text-brand-charcoal mb-2">Formulation</h4>
            <p className="text-xs text-gray-600">
              Delivery systems like liposomes, nanoparticles, and certain solvents can help larger molecules penetrate deeper into skin layers.
            </p>
          </div>
          
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <h4 className="font-medium text-brand-charcoal mb-2">Skin Condition</h4>
            <p className="text-xs text-gray-600">
              Damaged or compromised skin barriers allow deeper penetration, while intact barriers are more selective about what passes through.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngredientPenetrationChart;