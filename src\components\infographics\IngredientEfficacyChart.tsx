import React from 'react';
import { BarChart3, Star, TrendingUp } from 'lucide-react';

const IngredientEfficacyChart: React.FC = () => {
  const efficacyData = [
    {
      ingredient: 'Vitamin C',
      efficacy: 92,
      color: 'bg-orange-500',
      studies: '150+ studies',
      timeToResults: '4-6 weeks',
      notes: 'Gold standard for brightening',
    },
    {
      ingredient: 'Kojic Acid',
      efficacy: 78,
      color: 'bg-yellow-500',
      studies: '45+ studies',
      timeToResults: '6-8 weeks',
      notes: 'Gentle alternative',
    },
    {
      ingredient: 'Arbutin',
      efficacy: 71,
      color: 'bg-green-500',
      studies: '30+ studies',
      timeToResults: '8-10 weeks',
      notes: 'Natural hydroquinone alternative',
    },
    {
      ingredient: 'Licorice Root',
      efficacy: 65,
      color: 'bg-purple-500',
      studies: '25+ studies',
      timeToResults: '8-12 weeks',
      notes: 'Anti-inflammatory benefits',
    },
    {
      ingredient: 'Niacinamide',
      efficacy: 68,
      color: 'bg-blue-500',
      studies: '40+ studies',
      timeToResults: '6-8 weeks',
      notes: 'Multi-benefit ingredient',
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <BarChart3 className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Ingredient Efficacy for Hyperpigmentation
        </h2>
        <p className="text-gray-600">
          Comparative effectiveness based on clinical studies and research data
        </p>
      </div>

      <div className="space-y-6">
        {efficacyData.map((item, index) => (
          <div key={index} className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <h3 className="font-semibold text-brand-charcoal">{item.ingredient}</h3>
                {item.efficacy >= 90 && (
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-xs text-yellow-600">Top Rated</span>
                  </div>
                )}
              </div>
              <span className="text-lg font-bold text-brand-charcoal">{item.efficacy}%</span>
            </div>
            
            <div className="relative">
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`${item.color} h-3 rounded-full transition-all duration-1000 ease-out`}
                  style={{ width: `${item.efficacy}%` }}
                ></div>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">Research:</span> {item.studies}
              </div>
              <div>
                <span className="font-medium">Results:</span> {item.timeToResults}
              </div>
              <div>
                <span className="font-medium">Notes:</span> {item.notes}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold text-brand-charcoal mb-2">How to Read This Chart</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Efficacy percentages based on clinical study results and meta-analyses</li>
          <li>• Higher percentages indicate stronger evidence for treating hyperpigmentation</li>
          <li>• Time to results varies based on individual skin and severity of pigmentation</li>
          <li>• All ingredients shown are considered safe and effective when used properly</li>
        </ul>
      </div>
    </div>
  );
};

export default IngredientEfficacyChart;