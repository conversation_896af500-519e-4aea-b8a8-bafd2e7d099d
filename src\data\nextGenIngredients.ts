// Updated next-gen ingredients interface to remove products array
export interface NextGenIngredient {
  slug: string;
  name: string;
  scientificName: string;
  nicknames: string[];
  category: string;
  marketGrowth: string;
  projectedValue: string;
  trendingRegions: string[];
  description: string;
  whyTrending: string;
  keyIngredients?: string[];
  targetAudience: string[];
  quickFacts: {
    type: string;
    mainBenefit: string;
    bestFor: string[];
    avoidMixingWith: string[];
  };
  benefits: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
  howToUse: {
    routine: string;
    application: string;
    tips: string[];
  };
  research: Array<{
    title: string;
    journal: string;
    year: string;
    summary: string;
    pubmedId?: string;
    doi?: string;
    source?: string;
  }>;
  faqs: Array<{
    question: string;
    answer: string;
  }>;
  popularity: number;
  isTrending: boolean;
  isNextGen: boolean;
}