import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>R<PERSON>, Star, TrendingUp } from 'lucide-react';

const FeaturedIngredients: React.FC = () => {
  const featuredIngredients = [
    {
      slug: 'collagen',
      name: 'Collagen',
      category: 'Anti-Aging',
      description: 'The structural protein that keeps skin firm, plump, and youthful. Essential for maintaining skin elasticity and reducing signs of aging.',
      benefits: ['Firmness', 'Elasticity', 'Anti-aging'],
      popularity: 94,
      isTrending: true,
      image: 'https://images.pexels.com/photos/7290122/pexels-photo-7290122.jpeg?auto=compress&cs=tinysrgb&w=400',
    },
    {
      slug: 'hyaluronic-acid',
      name: 'Hyaluronic Acid',
      category: 'Hydration',
      description: 'A powerful humectant that can hold up to 1000 times its weight in water, providing intense hydration and plumping effects.',
      benefits: ['Deep hydration', 'Plumping', 'All skin types'],
      popularity: 96,
      isTrending: true,
      image: 'https://images.pexels.com/photos/7290720/pexels-photo-7290720.jpeg?auto=compress&cs=tinysrgb&w=400',
    },
    {
      slug: 'vitamin-c',
      name: 'Vitamin C',
      category: 'Antioxidant',
      description: 'A potent antioxidant that brightens skin, stimulates collagen production, and provides protection against environmental damage.',
      benefits: ['Brightening', 'Antioxidant', 'Collagen boost'],
      popularity: 92,
      isTrending: false,
      image: 'https://images.pexels.com/photos/4465124/pexels-photo-4465124.jpeg?auto=compress&cs=tinysrgb&w=400',
    },
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Featured Ingredients
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover the most popular and scientifically-backed ingredients that form 
            the foundation of effective skincare routines.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {featuredIngredients.map((ingredient, index) => (
            <Link
              key={ingredient.slug}
              to={`/ingredients/${ingredient.slug}`}
              className="card overflow-hidden hover:scale-105 transition-all duration-300 group animate-slide-up"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className="relative">
                <img
                  src={ingredient.image}
                  alt={ingredient.name}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="text-xs bg-white/90 backdrop-blur-sm text-gray-700 px-3 py-1 rounded-full font-medium">
                    {ingredient.category}
                  </span>
                </div>
                <div className="absolute top-4 right-4 flex items-center space-x-2">
                  {ingredient.isTrending && (
                    <div className="bg-orange-500 text-white p-1.5 rounded-full">
                      <TrendingUp className="w-3 h-3" />
                    </div>
                  )}
                  <div className="bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center space-x-1">
                    <Star className="w-3 h-3 text-yellow-500 fill-current" />
                    <span className="text-xs font-medium">{ingredient.popularity}</span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-brand-charcoal mb-3 group-hover:text-brand-teal transition-colors duration-200">
                  {ingredient.name}
                </h3>

                <p className="text-gray-600 mb-4 leading-relaxed text-sm">
                  {ingredient.description}
                </p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {ingredient.benefits.map((benefit, idx) => (
                    <span
                      key={idx}
                      className="text-xs bg-brand-teal/10 text-brand-teal px-3 py-1 rounded-full font-medium"
                    >
                      {benefit}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    Learn more
                  </span>
                  <ArrowRight className="w-5 h-5 text-brand-teal group-hover:translate-x-1 transition-transform duration-200" />
                </div>
              </div>
            </Link>
          ))}
        </div>

        <div className="text-center">
          <Link to="/ingredients" className="btn-primary">
            Explore All Ingredients
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedIngredients;