import React from 'react';
import { Sparkles, Shield, Zap, Droplets, Sun, Heart } from 'lucide-react';

const Categories: React.FC = () => {
  const categories = [
    {
      icon: Sparkles,
      name: 'Anti-Aging',
      count: 245,
      color: 'bg-brand-teal',
      description: 'Retinoids, peptides, and age-fighting compounds',
      popular: ['Retinol', 'Vitamin C', 'Peptides'],
    },
    {
      icon: Shield,
      name: 'Barrier Support',
      count: 189,
      color: 'bg-category-pink',
      description: 'Ceramides, fatty acids, and protective ingredients',
      popular: ['Niacinamide', 'Ceramides', 'Hyaluronic Acid'],
    },
    {
      icon: Zap,
      name: 'Exfoliants',
      count: 156,
      color: 'bg-category-lavender',
      description: 'AHAs, BHAs, and gentle exfoliating agents',
      popular: ['Salicylic Acid', 'Glycolic Acid', 'Lactic Acid'],
    },
    {
      icon: Droplets,
      name: 'Hydration',
      count: 198,
      color: 'bg-category-green',
      description: 'Humectants and moisture-binding compounds',
      popular: ['Hyaluronic Acid', 'Glycerin', 'Squalane'],
    },
    {
      icon: Sun,
      name: 'Sun Protection',
      count: 87,
      color: 'bg-category-blue',
      description: 'UV filters and photoprotective ingredients',
      popular: ['Zinc Oxide', 'Titanium Dioxide', 'Avobenzone'],
    },
    {
      icon: Heart,
      name: 'Sensitive Skin',
      count: 132,
      color: 'bg-category-yellow',
      description: 'Gentle, soothing, and calming ingredients',
      popular: ['Centella Asiatica', 'Allantoin', 'Bisabolol'],
    },
  ];

  return (
    <section className="section-padding bg-brand-off-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Explore by Category
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Navigate our comprehensive ingredient database organized by skincare concerns and benefits.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category, index) => (
            <div
              key={index}
              className="card p-6 hover:scale-105 transition-all duration-200 cursor-pointer group animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`inline-flex items-center justify-center w-12 h-12 ${category.color} rounded-lg group-hover:scale-110 transition-transform duration-200`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {category.count} ingredients
                </span>
              </div>
              
              <h3 className="text-xl font-semibold text-brand-charcoal mb-2">
                {category.name}
              </h3>
              
              <p className="text-gray-600 mb-4 text-sm">
                {category.description}
              </p>

              <div className="space-y-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Popular Ingredients
                </div>
                <div className="flex flex-wrap gap-2">
                  {category.popular.map((ingredient, idx) => (
                    <span
                      key={idx}
                      className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md hover:bg-brand-teal hover:text-white transition-colors duration-200"
                    >
                      {ingredient}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="btn-primary">
            View All Categories
          </button>
        </div>
      </div>
    </section>
  );
};

export default Categories;