import React from 'react';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

const IngredientCompatibilityMatrix: React.FC = () => {
  const ingredients = [
    'Vitamin C',
    'Retinol',
    'Niacinamide',
    'AHA/BHA',
    'Hyaluronic Acid',
    'Peptides',
    'Vitamin E',
  ];

  const compatibilityData: Record<string, Record<string, { status: string; note: string }>> = {
    'Vitamin C': {
      'Retinol': { status: 'avoid', note: 'Different pH requirements, use separately' },
      'Niacinamide': { status: 'excellent', note: 'Great brightening combination' },
      'AHA/BHA': { status: 'caution', note: 'May increase sensitivity, introduce gradually' },
      'Hyaluronic Acid': { status: 'excellent', note: 'Perfect hydrating combination' },
      'Peptides': { status: 'excellent', note: 'Excellent for collagen production' },
      'Vitamin E': { status: 'excellent', note: 'Enhanced stability and antioxidant protection' },
    },
    'Retinol': {
      'Vitamin C': { status: 'avoid', note: 'Different pH requirements, use separately' },
      'Niacinamide': { status: 'excellent', note: 'Niacinamide reduces retinol irritation' },
      'AHA/BHA': { status: 'avoid', note: 'Too much exfoliation, alternate days' },
      'Hyaluronic Acid': { status: 'excellent', note: 'HA helps counteract dryness from retinol' },
      'Peptides': { status: 'excellent', note: 'Complementary anti-aging benefits' },
      'Vitamin E': { status: 'excellent', note: 'Vitamin E enhances retinol stability' },
    },
    'Niacinamide': {
      'Vitamin C': { status: 'excellent', note: 'Great brightening combination' },
      'Retinol': { status: 'excellent', note: 'Reduces retinol irritation' },
      'AHA/BHA': { status: 'excellent', note: 'Reduces potential irritation from acids' },
      'Hyaluronic Acid': { status: 'excellent', note: 'Great for all skin types' },
      'Peptides': { status: 'excellent', note: 'Excellent for barrier repair' },
      'Vitamin E': { status: 'excellent', note: 'Good for barrier support' },
    },
    'AHA/BHA': {
      'Vitamin C': { status: 'caution', note: 'May increase sensitivity, introduce gradually' },
      'Retinol': { status: 'avoid', note: 'Too much exfoliation, alternate days' },
      'Niacinamide': { status: 'excellent', note: 'Reduces potential irritation from acids' },
      'Hyaluronic Acid': { status: 'excellent', note: 'Hydration helps balance exfoliation' },
      'Peptides': { status: 'caution', note: 'May affect peptide stability' },
      'Vitamin E': { status: 'excellent', note: 'Soothes skin after exfoliation' },
    },
    'Hyaluronic Acid': {
      'Vitamin C': { status: 'excellent', note: 'Perfect hydrating combination' },
      'Retinol': { status: 'excellent', note: 'Helps counteract dryness from retinol' },
      'Niacinamide': { status: 'excellent', note: 'Great for all skin types' },
      'AHA/BHA': { status: 'excellent', note: 'Hydration helps balance exfoliation' },
      'Peptides': { status: 'excellent', note: 'Enhanced hydration and anti-aging' },
      'Vitamin E': { status: 'excellent', note: 'Excellent hydration combination' },
    },
    'Peptides': {
      'Vitamin C': { status: 'excellent', note: 'Excellent for collagen production' },
      'Retinol': { status: 'excellent', note: 'Complementary anti-aging benefits' },
      'Niacinamide': { status: 'excellent', note: 'Excellent for barrier repair' },
      'AHA/BHA': { status: 'caution', note: 'May affect peptide stability' },
      'Hyaluronic Acid': { status: 'excellent', note: 'Enhanced hydration and anti-aging' },
      'Vitamin E': { status: 'excellent', note: 'Good for overall skin health' },
    },
    'Vitamin E': {
      'Vitamin C': { status: 'excellent', note: 'Enhanced stability and antioxidant protection' },
      'Retinol': { status: 'excellent', note: 'Vitamin E enhances retinol stability' },
      'Niacinamide': { status: 'excellent', note: 'Good for barrier support' },
      'AHA/BHA': { status: 'excellent', note: 'Soothes skin after exfoliation' },
      'Hyaluronic Acid': { status: 'excellent', note: 'Excellent hydration combination' },
      'Peptides': { status: 'excellent', note: 'Good for overall skin health' },
    },
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'caution':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'avoid':
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'caution':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'avoid':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Ingredient Compatibility Matrix
        </h2>
        <p className="text-gray-600">
          Visual guide to which skincare ingredients work well together and which to use separately
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="p-3 bg-gray-50 border border-gray-200"></th>
              {ingredients.map((ingredient, index) => (
                <th key={index} className="p-3 bg-gray-50 border border-gray-200 text-sm font-semibold text-brand-charcoal">
                  {ingredient}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {ingredients.map((rowIngredient, rowIndex) => (
              <tr key={rowIndex}>
                <th className="p-3 bg-gray-50 border border-gray-200 text-sm font-semibold text-brand-charcoal text-left">
                  {rowIngredient}
                </th>
                {ingredients.map((colIngredient, colIndex) => {
                  // Skip diagonal (same ingredient) and already filled cells
                  if (rowIndex === colIndex) {
                    return (
                      <td key={colIndex} className="p-3 border border-gray-200 bg-gray-100">
                        <div className="flex justify-center">—</div>
                      </td>
                    );
                  }
                  
                  // Skip cells that would be duplicates (matrix is symmetrical)
                  if (rowIndex > colIndex) {
                    return (
                      <td key={colIndex} className="p-3 border border-gray-200 bg-gray-50">
                        <div className="flex justify-center">—</div>
                      </td>
                    );
                  }
                  
                  const compatibility = compatibilityData[rowIngredient][colIngredient];
                  
                  return (
                    <td 
                      key={colIndex} 
                      className={`p-3 border ${getStatusColor(compatibility.status)}`}
                    >
                      <div className="flex flex-col items-center">
                        <div className="mb-1">{getStatusIcon(compatibility.status)}</div>
                        <div className="text-xs text-center">{compatibility.note}</div>
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-6 grid grid-cols-3 gap-4">
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <span className="text-sm text-gray-700">Excellent Combination</span>
        </div>
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-yellow-600" />
          <span className="text-sm text-gray-700">Use with Caution</span>
        </div>
        <div className="flex items-center space-x-2">
          <XCircle className="w-5 h-5 text-red-600" />
          <span className="text-sm text-gray-700">Avoid Combining</span>
        </div>
      </div>
    </div>
  );
};

export default IngredientCompatibilityMatrix;