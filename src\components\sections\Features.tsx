import React from 'react';
import { Beaker, BarChart3, GraduationCap, Eye, Users, Shield } from 'lucide-react';

const Features: React.FC = () => {
  const features = [
    {
      icon: Beaker,
      title: 'Comprehensive Database',
      description: 'Over 2,500 skincare ingredients with detailed scientific profiles, sourcing information, and usage guidelines.',
      color: 'bg-brand-teal',
    },
    {
      icon: BarChart3,
      title: 'Combination Intelligence',
      description: 'Discover which ingredients work together synergistically and which combinations to avoid for optimal results.',
      color: 'bg-category-pink',
    },
    {
      icon: GraduationCap,
      title: 'Beginner-Friendly Learning',
      description: 'Step-by-step guides and simplified explanations make complex skincare science accessible to everyone.',
      color: 'bg-category-lavender',
    },
    {
      icon: Eye,
      title: 'Visual Learning Tools',
      description: 'Interactive charts, infographics, and visual guides help you understand ingredients and their effects.',
      color: 'bg-category-green',
    },
    {
      icon: Users,
      title: 'Community Insights',
      description: 'Real user experiences and expert reviews provide practical context for ingredient effectiveness.',
      color: 'bg-category-blue',
    },
    {
      icon: Shield,
      title: 'Science-Backed Content',
      description: 'All information is researched and verified by skincare professionals and dermatology experts.',
      color: 'bg-category-yellow',
    },
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-brand-charcoal mb-6">
            Why Choose Skincare Compass?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We bridge the gap between complex skincare science and practical application, 
            making ingredient knowledge accessible to everyone.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="card p-8 hover:scale-105 transition-transform duration-200 animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`inline-flex items-center justify-center w-12 h-12 ${feature.color} rounded-lg mb-6`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>
              
              <h3 className="text-xl font-semibold text-brand-charcoal mb-4">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;