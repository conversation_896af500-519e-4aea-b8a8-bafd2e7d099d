import React from 'react';
import { Layers, ArrowDown, Clock, Droplets, Shield, Sun, Moon } from 'lucide-react';

const RoutineLayeringGuide: React.FC = () => {
  const morningSteps = [
    {
      name: 'Cleanse',
      description: 'Remove overnight buildup',
      texture: 'Water-based',
      icon: Droplets,
      color: 'bg-blue-500',
      tips: ['Use gentle cleanser', 'Lukewarm water only', 'Pat dry, don\'t rub'],
    },
    {
      name: 'Tone (Optional)',
      description: 'Balance pH and hydrate',
      texture: 'Watery',
      icon: Droplets,
      color: 'bg-blue-400',
      tips: ['Alcohol-free formula', 'Pat, don\'t rub', 'Allow to absorb'],
    },
    {
      name: 'Vitamin C Serum',
      description: 'Antioxidant protection',
      texture: 'Thin serum',
      icon: Shield,
      color: 'bg-orange-500',
      tips: ['Apply to dry skin', 'Wait 1-2 minutes', 'Use only in morning'],
    },
    {
      name: 'Hydrating Serum',
      description: 'Deep hydration',
      texture: 'Light serum',
      icon: Droplets,
      color: 'bg-teal-500',
      tips: ['Apply to damp skin', 'Focus on dry areas', 'Layer if needed'],
    },
    {
      name: 'Eye Cream',
      description: 'Target delicate eye area',
      texture: 'Light cream',
      icon: Droplets,
      color: 'bg-purple-500',
      tips: ['Use ring finger', 'Gentle tapping motion', 'Avoid too close to eyes'],
    },
    {
      name: 'Moisturizer',
      description: 'Seal in hydration',
      texture: 'Cream or lotion',
      icon: Shield,
      color: 'bg-green-500',
      tips: ['Wait for serums to absorb', 'Apply evenly', 'Don\'t forget neck'],
    },
    {
      name: 'Sunscreen',
      description: 'UV protection (essential)',
      texture: 'Varies',
      icon: Sun,
      color: 'bg-yellow-500',
      tips: ['Apply generously', 'Reapply every 2 hours', 'SPF 30+ minimum'],
    },
  ];

  const eveningSteps = [
    {
      name: 'Oil Cleanser',
      description: 'Remove makeup & sunscreen',
      texture: 'Oil-based',
      icon: Droplets,
      color: 'bg-amber-500',
      tips: ['Massage for 1 minute', 'Emulsify with water', 'Rinse thoroughly'],
    },
    {
      name: 'Water-Based Cleanser',
      description: 'Remove remaining impurities',
      texture: 'Water-based',
      icon: Droplets,
      color: 'bg-blue-500',
      tips: ['Gentle circular motions', 'Rinse thoroughly', 'Pat dry'],
    },
    {
      name: 'Exfoliant (2-3x/week)',
      description: 'Remove dead skin cells',
      texture: 'Liquid or gel',
      icon: Shield,
      color: 'bg-pink-500',
      tips: ['Use only 2-3x weekly', 'Avoid with retinol', 'Follow instructions'],
    },
    {
      name: 'Treatment Serum',
      description: 'Target specific concerns',
      texture: 'Thin serum',
      icon: Shield,
      color: 'bg-purple-500',
      tips: ['Apply to dry skin', 'Use appropriate amount', 'Allow to absorb'],
    },
    {
      name: 'Retinol (PM only)',
      description: 'Cell renewal & anti-aging',
      texture: 'Serum or cream',
      icon: Moon,
      color: 'bg-indigo-500',
      tips: ['Start 1-2x weekly', 'Pea-sized amount', 'Avoid eye area'],
    },
    {
      name: 'Hydrating Serum',
      description: 'Deep hydration',
      texture: 'Light serum',
      icon: Droplets,
      color: 'bg-teal-500',
      tips: ['Layer if needed', 'Pat gently', 'Focus on dry areas'],
    },
    {
      name: 'Eye Cream',
      description: 'Target delicate eye area',
      texture: 'Light cream',
      icon: Droplets,
      color: 'bg-purple-400',
      tips: ['Use ring finger', 'Gentle tapping motion', 'Avoid too close to eyes'],
    },
    {
      name: 'Moisturizer',
      description: 'Seal in treatments & hydration',
      texture: 'Cream',
      icon: Shield,
      color: 'bg-green-600',
      tips: ['Apply generously', 'Don\'t forget neck', 'Allow to absorb'],
    },
    {
      name: 'Face Oil (Optional)',
      description: 'Extra nourishment',
      texture: 'Oil',
      icon: Droplets,
      color: 'bg-yellow-600',
      tips: ['2-3 drops only', 'Press, don\'t rub', 'Final step'],
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <Layers className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Complete Skincare Layering Guide
        </h2>
        <p className="text-gray-600">
          The correct order to apply your skincare products for maximum effectiveness
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        {/* Morning Routine */}
        <div>
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <Sun className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-brand-charcoal">Morning Routine</h3>
              <p className="text-gray-600 text-sm">Focus: Protection & Hydration</p>
            </div>
          </div>

          <div className="space-y-4">
            {morningSteps.map((step, index) => (
              <div key={index} className="relative">
                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-brand-teal transition-colors duration-200">
                  <div className={`w-10 h-10 ${step.color} rounded-full flex items-center justify-center text-white flex-shrink-0`}>
                    <step.icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-semibold text-brand-charcoal">{step.name}</h4>
                      <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">{step.texture}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {step.tips.map((tip, idx) => (
                        <span key={idx} className="text-xs bg-white text-gray-600 px-2 py-1 rounded-full border border-gray-200">
                          {tip}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                {index < morningSteps.length - 1 && (
                  <div className="flex justify-center my-2">
                    <ArrowDown className="w-5 h-5 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Evening Routine */}
        <div>
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Moon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-brand-charcoal">Evening Routine</h3>
              <p className="text-gray-600 text-sm">Focus: Treatment & Repair</p>
            </div>
          </div>

          <div className="space-y-4">
            {eveningSteps.map((step, index) => (
              <div key={index} className="relative">
                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-brand-teal transition-colors duration-200">
                  <div className={`w-10 h-10 ${step.color} rounded-full flex items-center justify-center text-white flex-shrink-0`}>
                    <step.icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-semibold text-brand-charcoal">{step.name}</h4>
                      <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded-full">{step.texture}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {step.tips.map((tip, idx) => (
                        <span key={idx} className="text-xs bg-white text-gray-600 px-2 py-1 rounded-full border border-gray-200">
                          {tip}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                {index < eveningSteps.length - 1 && (
                  <div className="flex justify-center my-2">
                    <ArrowDown className="w-5 h-5 text-gray-400" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <div className="flex items-center space-x-2 mb-3">
          <Clock className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-blue-800">Waiting Times Between Steps</h3>
        </div>
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div className="bg-white rounded-lg p-3 border border-blue-200">
            <span className="font-medium text-blue-700">Active Ingredients</span>
            <p className="text-blue-600">Wait 1-2 minutes between actives</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-blue-200">
            <span className="font-medium text-blue-700">Vitamin C → Moisturizer</span>
            <p className="text-blue-600">Wait 5 minutes for absorption</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-blue-200">
            <span className="font-medium text-blue-700">Retinol → Next Step</span>
            <p className="text-blue-600">Wait 10-20 minutes after applying</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoutineLayeringGuide;