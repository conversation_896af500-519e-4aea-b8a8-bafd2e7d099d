import React from 'react';
import { Droplets, Zap, Shield, Flame, HelpCircle } from 'lucide-react';

const SkinTypeDecisionTree: React.FC = () => {
  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <HelpCircle className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Skin Type Decision Tree
        </h2>
        <p className="text-gray-600">
          Interactive flowchart to help identify your skin type
        </p>
      </div>

      <div className="max-w-4xl mx-auto">
        {/* Starting Question */}
        <div className="bg-blue-50 rounded-lg p-6 text-center mb-8">
          <h3 className="text-xl font-bold text-blue-800 mb-4">How does your skin feel a few hours after washing?</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 border border-blue-200 hover:border-blue-500 transition-colors duration-200 cursor-pointer">
              <div className="flex flex-col items-center">
                <Droplets className="w-8 h-8 text-blue-500 mb-2" />
                <span className="font-semibold text-blue-700">Tight & Dry</span>
                <span className="text-xs text-blue-600 mt-1">→ Likely Dry Skin</span>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-blue-200 hover:border-blue-500 transition-colors duration-200 cursor-pointer">
              <div className="flex flex-col items-center">
                <Shield className="w-8 h-8 text-blue-500 mb-2" />
                <span className="font-semibold text-blue-700">Comfortable</span>
                <span className="text-xs text-blue-600 mt-1">→ Likely Normal Skin</span>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-blue-200 hover:border-blue-500 transition-colors duration-200 cursor-pointer">
              <div className="flex flex-col items-center">
                <Zap className="w-8 h-8 text-blue-500 mb-2" />
                <span className="font-semibold text-blue-700">Oily/Shiny</span>
                <span className="text-xs text-blue-600 mt-1">→ Likely Oily Skin</span>
              </div>
            </div>
          </div>
        </div>

        {/* Follow-up Questions */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* Dry Skin Path */}
          <div className="space-y-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">If you chose "Tight & Dry":</h4>
              <p className="text-sm text-blue-700 mb-3">Does your skin also feel itchy or flaky?</p>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white rounded-lg p-3 border border-blue-200 text-center">
                  <span className="text-sm font-medium text-blue-700">Yes</span>
                  <p className="text-xs text-blue-600 mt-1">Very Dry Skin</p>
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-200 text-center">
                  <span className="text-sm font-medium text-blue-700">No</span>
                  <p className="text-xs text-blue-600 mt-1">Normal-to-Dry Skin</p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2">Dry Skin Recommendations:</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Rich, cream cleansers</li>
                <li>• Hydrating serums with hyaluronic acid</li>
                <li>• Moisturizers with ceramides</li>
                <li>• Avoid alcohol and fragrance</li>
                <li>• Consider facial oils</li>
              </ul>
            </div>
          </div>

          {/* Oily Skin Path */}
          <div className="space-y-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-semibold text-blue-800 mb-2">If you chose "Oily/Shiny":</h4>
              <p className="text-sm text-blue-700 mb-3">Is your T-zone (forehead, nose, chin) oilier than your cheeks?</p>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-white rounded-lg p-3 border border-blue-200 text-center">
                  <span className="text-sm font-medium text-blue-700">Yes</span>
                  <p className="text-xs text-blue-600 mt-1">Combination Skin</p>
                </div>
                <div className="bg-white rounded-lg p-3 border border-blue-200 text-center">
                  <span className="text-sm font-medium text-blue-700">No</span>
                  <p className="text-xs text-blue-600 mt-1">Oily Skin</p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-semibold text-green-800 mb-2">Oily Skin Recommendations:</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Gel or foam cleansers</li>
                <li>• Oil-free, lightweight moisturizers</li>
                <li>• Niacinamide for oil control</li>
                <li>• Salicylic acid for pore clearing</li>
                <li>• Non-comedogenic products</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Sensitive Skin Section */}
        <div className="mt-8 bg-red-50 rounded-lg p-6">
          <h3 className="text-xl font-bold text-red-800 mb-4 text-center">Sensitive Skin Overlay</h3>
          <p className="text-red-700 text-center mb-4">
            Any skin type can also be sensitive. Answer these questions to determine if you have sensitive skin:
          </p>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-red-800">Signs of Sensitive Skin:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• Skin reacts easily to new products</li>
                <li>• Frequent redness or flushing</li>
                <li>• Burning or stinging sensations</li>
                <li>• Prone to rashes or bumps</li>
                <li>• Easily irritated by weather changes</li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-semibold text-red-800">Sensitive Skin Recommendations:</h4>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• Fragrance-free products</li>
                <li>• Minimal ingredient lists</li>
                <li>• Patch test everything</li>
                <li>• Avoid common irritants</li>
                <li>• Focus on barrier repair</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Final Notes */}
        <div className="mt-8 bg-gray-50 rounded-lg p-6">
          <h3 className="font-semibold text-gray-800 mb-3">Important Notes:</h3>
          <ul className="text-sm text-gray-700 space-y-2">
            <li>• Your skin type can change with seasons, hormones, and age</li>
            <li>• Dehydrated skin (lacking water) is different from dry skin (lacking oil)</li>
            <li>• For a professional assessment, consult a dermatologist</li>
            <li>• Skin type is just one factor in choosing products - concerns and goals matter too</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SkinTypeDecisionTree;