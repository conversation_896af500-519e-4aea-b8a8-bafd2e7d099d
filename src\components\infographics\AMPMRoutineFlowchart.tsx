import React from 'react';
import { <PERSON>, <PERSON>, ArrowDown, Clock, AlertTriangle } from 'lucide-react';

const AMPMRoutineFlowchart: React.FC = () => {
  const amRoutine = [
    {
      step: 1,
      product: 'Gentle Cleanser',
      ingredients: ['Ceramides', 'Glycerin'],
      why: 'Remove overnight buildup',
      icon: '🧼',
    },
    {
      step: 2,
      product: 'Vitamin C Serum',
      ingredients: ['L-Ascorbic Acid', 'Ferulic Acid'],
      why: 'Antioxidant protection',
      icon: '🍊',
    },
    {
      step: 3,
      product: 'Moisturizer',
      ingredients: ['Hyaluronic Acid', 'Niacinamide'],
      why: 'Hydration & barrier support',
      icon: '💧',
    },
    {
      step: 4,
      product: 'Sunscreen SPF 30+',
      ingredients: ['Zinc Oxide', 'Titanium Dioxide'],
      why: 'UV protection (essential!)',
      icon: '☀️',
    },
  ];

  const pmRoutine = [
    {
      step: 1,
      product: 'Oil Cleanser (if wearing makeup)',
      ingredients: ['Jojoba Oil', 'Emulsifiers'],
      why: 'Remove makeup & sunscreen',
      icon: '🫧',
    },
    {
      step: 2,
      product: 'Water-Based Cleanser',
      ingredients: ['Gentle surfactants'],
      why: 'Deep clean without stripping',
      icon: '🧼',
    },
    {
      step: 3,
      product: 'Treatment (2-3x/week)',
      ingredients: ['Retinol', 'AHA/BHA'],
      why: 'Cell renewal & exfoliation',
      icon: '✨',
    },
    {
      step: 4,
      product: 'Hydrating Serum',
      ingredients: ['Hyaluronic Acid', 'Peptides'],
      why: 'Repair & hydration',
      icon: '💧',
    },
    {
      step: 5,
      product: 'Night Moisturizer',
      ingredients: ['Ceramides', 'Squalane'],
      why: 'Overnight repair',
      icon: '🌙',
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <Clock className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          AM vs. PM Routine Flowchart
        </h2>
        <p className="text-gray-600">
          Visual guide to optimal ingredient timing for maximum effectiveness
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* AM Routine */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <Sun className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-brand-charcoal">Morning Routine</h3>
              <p className="text-gray-600 text-sm">Focus: Protection & Hydration</p>
            </div>
          </div>

          {amRoutine.map((step, index) => (
            <div key={index} className="relative">
              <div className="flex items-start space-x-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                  {step.step}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-brand-charcoal">{step.product}</h4>
                    <span className="text-xl">{step.icon}</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{step.why}</p>
                  <div className="flex flex-wrap gap-1">
                    {step.ingredients.map((ingredient, idx) => (
                      <span key={idx} className="text-xs bg-yellow-200 text-yellow-800 px-2 py-1 rounded-full">
                        {ingredient}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              {index < amRoutine.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowDown className="w-5 h-5 text-gray-400" />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* PM Routine */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Moon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-brand-charcoal">Evening Routine</h3>
              <p className="text-gray-600 text-sm">Focus: Repair & Renewal</p>
            </div>
          </div>

          {pmRoutine.map((step, index) => (
            <div key={index} className="relative">
              <div className="flex items-start space-x-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                  {step.step}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h4 className="font-semibold text-brand-charcoal">{step.product}</h4>
                    <span className="text-xl">{step.icon}</span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{step.why}</p>
                  <div className="flex flex-wrap gap-1">
                    {step.ingredients.map((ingredient, idx) => (
                      <span key={idx} className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        {ingredient}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              {index < pmRoutine.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowDown className="w-5 h-5 text-gray-400" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Key Principles */}
      <div className="mt-8 grid md:grid-cols-2 gap-6">
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <h4 className="font-semibold text-green-800 mb-2 flex items-center space-x-2">
            <span>✅</span>
            <span>Morning Principles</span>
          </h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• Use antioxidants for protection</li>
            <li>• Always end with SPF</li>
            <li>• Lighter textures work better</li>
            <li>• Focus on hydration & barrier support</li>
          </ul>
        </div>

        <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-semibold text-purple-800 mb-2 flex items-center space-x-2">
            <span>🌙</span>
            <span>Evening Principles</span>
          </h4>
          <ul className="text-sm text-purple-700 space-y-1">
            <li>• Use actives for repair & renewal</li>
            <li>• Richer textures for overnight repair</li>
            <li>• Double cleanse if wearing makeup</li>
            <li>• Focus on treatment & recovery</li>
          </ul>
        </div>
      </div>

      <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="flex items-start space-x-2">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-semibold text-yellow-800 mb-1">Important Notes</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Start with basic routine, add treatments gradually</li>
              <li>• Use actives (retinol, acids) only 2-3x per week initially</li>
              <li>• Always patch test new products</li>
              <li>• Consistency is more important than having many products</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AMPMRoutineFlowchart;