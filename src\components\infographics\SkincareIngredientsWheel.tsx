import React from 'react';
import { Droplets, Shield, Zap, Sparkles, Sun, Heart } from 'lucide-react';

const SkincareIngredientsWheel: React.FC = () => {
  const categories = [
    {
      name: 'Hydrators',
      icon: Droplets,
      color: 'bg-blue-500',
      textColor: 'text-blue-800',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      ingredients: [
        { name: 'Hyaluronic Acid', benefit: 'Holds 1000x its weight in water' },
        { name: 'Glycerin', benefit: 'Humectant that draws in moisture' },
        { name: 'Squalane', benefit: 'Lightweight, non-greasy hydration' },
        { name: 'Panthenol', benefit: 'Soothes and hydrates' },
      ],
    },
    {
      name: 'Antioxidants',
      icon: Shield,
      color: 'bg-orange-500',
      textColor: 'text-orange-800',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      ingredients: [
        { name: 'Vitamin C', benefit: 'Brightens and protects' },
        { name: 'Vitamin E', benefit: 'Moisturizes and heals' },
        { name: 'Ferulic Acid', benefit: 'Enhances other antioxidants' },
        { name: 'Green Tea', benefit: 'Calms inflammation' },
      ],
    },
    {
      name: 'Exfoliants',
      icon: Zap,
      color: 'bg-pink-500',
      textColor: 'text-pink-800',
      bgColor: 'bg-pink-50',
      borderColor: 'border-pink-200',
      ingredients: [
        { name: 'Glycolic Acid', benefit: 'Surface cell renewal' },
        { name: 'Salicylic Acid', benefit: 'Unclogs pores' },
        { name: 'Lactic Acid', benefit: 'Gentle exfoliation' },
        { name: 'Enzymes', benefit: 'Natural exfoliation' },
      ],
    },
    {
      name: 'Anti-Aging',
      icon: Sparkles,
      color: 'bg-purple-500',
      textColor: 'text-purple-800',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      ingredients: [
        { name: 'Retinol', benefit: 'Gold standard for anti-aging' },
        { name: 'Peptides', benefit: 'Supports collagen production' },
        { name: 'Bakuchiol', benefit: 'Natural retinol alternative' },
        { name: 'Niacinamide', benefit: 'Improves elasticity' },
      ],
    },
    {
      name: 'Protectors',
      icon: Sun,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-800',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      ingredients: [
        { name: 'Zinc Oxide', benefit: 'Physical sun protection' },
        { name: 'Titanium Dioxide', benefit: 'Gentle mineral sunscreen' },
        { name: 'Iron Oxides', benefit: 'Blue light protection' },
        { name: 'Ceramides', benefit: 'Strengthens skin barrier' },
      ],
    },
    {
      name: 'Soothers',
      icon: Heart,
      color: 'bg-green-500',
      textColor: 'text-green-800',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      ingredients: [
        { name: 'Centella Asiatica', benefit: 'Calms irritation' },
        { name: 'Aloe Vera', benefit: 'Soothes and hydrates' },
        { name: 'Oat Extract', benefit: 'Relieves itching' },
        { name: 'Allantoin', benefit: 'Promotes healing' },
      ],
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Essential Skincare Ingredients Wheel
        </h2>
        <p className="text-gray-600">
          A visual guide to the most effective ingredients by category
        </p>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category, index) => (
          <div 
            key={index} 
            className={`rounded-xl border ${category.borderColor} ${category.bgColor} overflow-hidden animate-slide-up`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`${category.color} p-4 text-white`}>
              <div className="flex items-center space-x-3">
                <category.icon className="w-6 h-6" />
                <h3 className="text-lg font-bold">{category.name}</h3>
              </div>
            </div>
            
            <div className="p-4">
              <ul className="space-y-3">
                {category.ingredients.map((ingredient, idx) => (
                  <li key={idx} className="flex items-start space-x-2">
                    <div className={`w-2 h-2 ${category.color} rounded-full mt-2 flex-shrink-0`}></div>
                    <div>
                      <span className={`font-medium ${category.textColor}`}>{ingredient.name}</span>
                      <p className="text-xs text-gray-600">{ingredient.benefit}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold text-brand-charcoal mb-3">How to Use This Guide:</h3>
        <p className="text-sm text-gray-600 mb-4">
          Identify your primary skin concerns and select ingredients from the appropriate categories. 
          For a well-rounded routine, include at least one ingredient from each of these essential categories.
        </p>
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
            <span className="font-medium text-blue-800">Beginner Tip</span>
            <p className="text-blue-700 text-xs">Start with one ingredient from each category</p>
          </div>
          <div className="bg-purple-50 rounded-lg p-3 border border-purple-100">
            <span className="font-medium text-purple-800">Intermediate Tip</span>
            <p className="text-purple-700 text-xs">Layer compatible ingredients for synergy</p>
          </div>
          <div className="bg-green-50 rounded-lg p-3 border border-green-100">
            <span className="font-medium text-green-800">Advanced Tip</span>
            <p className="text-green-700 text-xs">Rotate ingredients based on skin needs</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkincareIngredientsWheel;