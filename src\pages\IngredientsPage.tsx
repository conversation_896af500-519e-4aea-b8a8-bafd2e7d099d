import React, { useState } from 'react';
import { Search, Filter, Grid, List, Beaker, TrendingUp, Clock, Star, Zap, Sparkles, Target, Users } from 'lucide-react';
import { Link } from 'react-router-dom';
import { ingredients } from '../data/ingredients';
import { additionalIngredients } from '../data/additionalIngredients';
import MetaTags from '../components/SEO/MetaTags';
import IngredientPenetrationChart from '../components/infographics/IngredientPenetrationChart';
import SkinConcernsTargetingGuide from '../components/infographics/SkinConcernsTargetingGuide';
import { AdSidebar } from '../components/ads';

const IngredientsPage: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTrend, setSelectedTrend] = useState('all');
  const [sortBy, setSortBy] = useState('popular');

  // Convert ingredients object to array and combine with additional ingredients
  const mainIngredientsArray = Object.values(ingredients);
  const allIngredientsArray = [...mainIngredientsArray, ...additionalIngredients];

  // Create a unique list of categories from all ingredients
  const uniqueCategories = Array.from(new Set(allIngredientsArray.map(ing => ing.category)));

  const categories = [
    { id: 'all', name: 'All Categories', count: allIngredientsArray.length },
    ...uniqueCategories.map(category => ({
      id: category.toLowerCase().replace(/\s+/g, '-'),
      name: category,
      count: allIngredientsArray.filter(i => i.category === category).length
    }))
  ];

  const trendFilters = [
    { id: 'all', name: 'All Ingredients' },
    { id: 'trending', name: 'Trending Now' },
    { id: 'next-gen', name: 'Next-Generation' },
    { id: 'new', name: 'New Discoveries' },
    { id: 'high-growth', name: 'High Growth' },
  ];

  const filteredIngredients = allIngredientsArray.filter(ingredient => {
    const matchesSearch = ingredient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ingredient.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (ingredient.scientificName && ingredient.scientificName.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         ('nicknames' in ingredient && ingredient.nicknames && 
                          ingredient.nicknames.some(nick => nick.toLowerCase().includes(searchTerm.toLowerCase()))) ||
                         ('keyIngredients' in ingredient && ingredient.keyIngredients && 
                          ingredient.keyIngredients.some(key => key.toLowerCase().includes(searchTerm.toLowerCase())));
    
    const matchesCategory = selectedCategory === 'all' || 
                           ingredient.category.toLowerCase().replace(/\s+/g, '-') === selectedCategory;
    
    const matchesTrend = selectedTrend === 'all' ||
                        (selectedTrend === 'trending' && 'isTrending' in ingredient && ingredient.isTrending) ||
                        (selectedTrend === 'next-gen' && 'isNextGen' in ingredient && ingredient.isNextGen) ||
                        (selectedTrend === 'new' && 'isNew' in ingredient && ingredient.isNew) ||
                        (selectedTrend === 'high-growth' && 'growthRate' in ingredient && ingredient.growthRate && 
                         parseInt(ingredient.growthRate.replace(/[^0-9]/g, '')) > 100);
    
    return matchesSearch && matchesCategory && matchesTrend;
  });

  const sortedIngredients = [...filteredIngredients].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return ('popularity' in b ? b.popularity : 50) - ('popularity' in a ? a.popularity : 50);
      case 'alphabetical':
        return a.name.localeCompare(b.name);
      case 'newest':
        return ('isNew' in b ? (b.isNew ? 1 : 0) : 0) - ('isNew' in a ? (a.isNew ? 1 : 0) : 0);
      case 'trending':
        return ('isTrending' in b ? (b.isTrending ? 1 : 0) : 0) - ('isTrending' in a ? (a.isTrending ? 1 : 0) : 0);
      case 'growth':
        const aGrowth = 'growthRate' in a && a.growthRate ? parseInt(a.growthRate.replace(/[^0-9]/g, '')) : 0;
        const bGrowth = 'growthRate' in b && b.growthRate ? parseInt(b.growthRate.replace(/[^0-9]/g, '')) : 0;
        return bGrowth - aGrowth;
      default:
        return 0;
    }
  });

  // Get top trending ingredients for highlights
  const topTrending = mainIngredientsArray
    .filter(i => i.growthRate && parseInt(i.growthRate.replace(/[^0-9]/g, '')) > 100)
    .sort((a, b) => {
      const aGrowth = parseInt(a.growthRate!.replace(/[^0-9]/g, ''));
      const bGrowth = parseInt(b.growthRate!.replace(/[^0-9]/g, ''));
      return bGrowth - aGrowth;
    })
    .slice(0, 4);

  // SEO data
  const seoTitle = 'Complete Skincare Ingredient Directory - 2,500+ Ingredients Explained';
  const seoDescription = 'Explore our comprehensive database of skincare ingredients with scientific profiles, benefits, usage guidelines, and real product recommendations. From trending ingredients like exosome serums to classics like vitamin C.';
  const seoKeywords = 'skincare ingredients, ingredient database, skincare science, cosmetic ingredients, skincare guide, ingredient benefits, skincare education, trending ingredients, next-gen skincare';

  return (
    <div className="pt-16 min-h-screen bg-brand-off-white">
      <MetaTags
        title={seoTitle}
        description={seoDescription}
        keywords={seoKeywords}
        canonicalUrl="https://www.skincarecompass.com/ingredients/"
      />

      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Ingredient Directory
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our comprehensive database of over 2,500 skincare ingredients with detailed 
              scientific profiles, benefits, and usage guidelines. From trending discoveries to time-tested classics.
            </p>
          </div>

          {/* Next-Gen Spotlight */}
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 mb-8">
            <div className="flex items-center space-x-2 mb-4">
              <Sparkles className="w-5 h-5 text-purple-500" />
              <h2 className="text-lg font-semibold text-purple-800">Next-Generation Ingredients</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
              <div className="bg-white rounded-lg p-4">
                <div className="font-bold text-purple-600 mb-1">Bio-Retinol Revolution</div>
                <div className="text-purple-700">+45% CAGR - Gentle plant-based alternatives</div>
              </div>
              <div className="bg-white rounded-lg p-4">
                <div className="font-bold text-purple-600 mb-1">Exosome Therapy</div>
                <div className="text-purple-700">$400M+ by 2025 - Cellular regeneration</div>
              </div>
              <div className="bg-white rounded-lg p-4">
                <div className="font-bold text-purple-600 mb-1">AI-Personalized Actives</div>
                <div className="text-purple-700">Custom formulations via skin diagnostics</div>
              </div>
            </div>
          </div>

          {/* Trending Highlights */}
          <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 mb-8">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="w-5 h-5 text-orange-500" />
              <h2 className="text-lg font-semibold text-orange-800">Trending Now</h2>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              {topTrending.map((ingredient, index) => (
                <div key={index} className="text-center">
                  <div className="font-bold text-orange-600">{ingredient.growthRate}</div>
                  <div className="text-orange-700">{ingredient.name}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Search and Filters */}
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search ingredients, benefits, or brands..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name} ({category.count})
                  </option>
                ))}
              </select>
              <select
                value={selectedTrend}
                onChange={(e) => setSelectedTrend(e.target.value)}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
              >
                {trendFilters.map(filter => (
                  <option key={filter.id} value={filter.id}>
                    {filter.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">Sort by:</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-brand-teal focus:border-transparent"
                >
                  <option value="popular">Most Popular</option>
                  <option value="alphabetical">Alphabetical</option>
                  <option value="newest">Newest</option>
                  <option value="trending">Trending</option>
                  <option value="growth">Highest Growth</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-colors duration-200 ${
                    viewMode === 'grid' ? 'bg-brand-teal text-white' : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-colors duration-200 ${
                    viewMode === 'list' ? 'bg-brand-teal text-white' : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Ingredient Penetration Chart */}
      <div className="container-custom py-12">
        <IngredientPenetrationChart />
      </div>

      {/* Results */}
      <div className="container-custom py-12">
        <div className="flex gap-8">
          {/* Main Content */}
          <div className="flex-1">
            <div className="mb-6 flex justify-between items-center">
              <p className="text-gray-600">
                Showing {sortedIngredients.length} of {allIngredientsArray.length} ingredients
              </p>
              {searchTerm && (
                <p className="text-sm text-gray-500">
                  Search results for "{searchTerm}"
                </p>
              )}
            </div>

        {viewMode === 'grid' ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {sortedIngredients.map((ingredient, index) => (
              <Link
                key={ingredient.slug}
                to={`/ingredients/${ingredient.slug}`}
                className="card p-6 hover:scale-105 transition-all duration-200 group animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Beaker className="w-5 h-5 text-brand-teal" />
                    {'isTrending' in ingredient && ingredient.isTrending && (
                      <TrendingUp className="w-4 h-4 text-orange-500" />
                    )}
                    {'isNextGen' in ingredient && ingredient.isNextGen && (
                      <Sparkles className="w-4 h-4 text-purple-500" />
                    )}
                    {'isNew' in ingredient && ingredient.isNew && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                        New
                      </span>
                    )}
                  </div>
                  {'popularity' in ingredient && (
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm text-gray-600">{ingredient.popularity}</span>
                    </div>
                  )}
                </div>

                <h3 className="text-xl font-semibold text-brand-charcoal mb-2 group-hover:text-brand-teal transition-colors duration-200">
                  {ingredient.name}
                </h3>

                <p className="text-sm text-gray-500 mb-3">{ingredient.category}</p>

                {/* Market Data */}
                {'monthlySearches' in ingredient && (ingredient.monthlySearches || ingredient.marketGrowth) && (
                  <div className="flex items-center space-x-2 mb-3">
                    <Zap className="w-4 h-4 text-orange-500" />
                    <div className="text-xs">
                      {ingredient.monthlySearches && (
                        <span className="text-orange-600 font-medium mr-2">
                          {ingredient.monthlySearches} searches
                        </span>
                      )}
                      {ingredient.marketGrowth && (
                        <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                          {ingredient.marketGrowth}
                        </span>
                      )}
                      {ingredient.growthRate && (
                        <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full ml-1">
                          {ingredient.growthRate}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Why Trending */}
                {'whyTrending' in ingredient && ingredient.whyTrending && (
                  <div className="mb-3">
                    <div className="text-xs font-medium text-purple-600 mb-1">Why Trending:</div>
                    <p className="text-xs text-purple-700 line-clamp-2">{ingredient.whyTrending}</p>
                  </div>
                )}

                <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3">
                  {ingredient.description}
                </p>

                <div className="flex flex-wrap gap-2">
                  {'quickFacts' in ingredient && ingredient.quickFacts.bestFor ? 
                    ingredient.quickFacts.bestFor.slice(0, 3).map((benefit, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md"
                      >
                        {benefit}
                      </span>
                    )) :
                    'bestFor' in ingredient && ingredient.bestFor.slice(0, 3).map((benefit, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md"
                      >
                        {benefit}
                      </span>
                    ))
                  }
                </div>

                {/* Target Audience */}
                {'targetAudience' in ingredient && ingredient.targetAudience && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <div className="flex items-center space-x-1 mb-2">
                      <Target className="w-3 h-3 text-gray-400" />
                      <span className="text-xs text-gray-500">Target:</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {ingredient.targetAudience.slice(0, 2).map((audience, idx) => (
                        <span
                          key={idx}
                          className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-md"
                        >
                          {audience}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </Link>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {sortedIngredients.map((ingredient, index) => (
              <Link
                key={ingredient.slug}
                to={`/ingredients/${ingredient.slug}`}
                className="card p-6 hover:shadow-md transition-all duration-200 group animate-slide-up"
                style={{ animationDelay: `${index * 0.05}s` }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-xl font-semibold text-brand-charcoal group-hover:text-brand-teal transition-colors duration-200">
                        {ingredient.name}
                      </h3>
                      {'isTrending' in ingredient && ingredient.isTrending && (
                        <TrendingUp className="w-4 h-4 text-orange-500" />
                      )}
                      {'isNextGen' in ingredient && ingredient.isNextGen && (
                        <Sparkles className="w-4 h-4 text-purple-500" />
                      )}
                      {'isNew' in ingredient && ingredient.isNew && (
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                          New
                        </span>
                      )}
                      {'monthlySearches' in ingredient && ingredient.monthlySearches && (
                        <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                          {ingredient.monthlySearches}
                        </span>
                      )}
                      {'marketGrowth' in ingredient && ingredient.marketGrowth && (
                        <span className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
                          {ingredient.marketGrowth}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-500 mb-2">{ingredient.category}</p>
                    {'whyTrending' in ingredient && ingredient.whyTrending && (
                      <p className="text-sm text-purple-600 mb-2 font-medium">
                        Why trending: {ingredient.whyTrending}
                      </p>
                    )}
                    <p className="text-gray-600 mb-3 line-clamp-2">{ingredient.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {'quickFacts' in ingredient && ingredient.quickFacts.bestFor ? 
                        ingredient.quickFacts.bestFor.map((benefit, idx) => (
                          <span
                            key={idx}
                            className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md"
                          >
                            {benefit}
                          </span>
                        )) :
                        'bestFor' in ingredient && ingredient.bestFor.map((benefit, idx) => (
                          <span
                            key={idx}
                            className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-md"
                          >
                            {benefit}
                          </span>
                        ))
                      }
                    </div>
                  </div>
                  <div className="ml-6 text-right">
                    {'popularity' in ingredient && (
                      <div className="flex items-center space-x-1 mb-2">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm text-gray-600">{ingredient.popularity}</span>
                      </div>
                    )}
                    <Beaker className="w-8 h-8 text-brand-teal mx-auto" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}

        {sortedIngredients.length === 0 && (
          <div className="text-center py-12">
            <Beaker className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No ingredients found</h3>
            <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
          </div>
        )}
          </div>

          {/* Sidebar */}
          <div className="hidden lg:block w-64">
            <AdSidebar />
          </div>
        </div>
      </div>

      {/* Skin Concerns Targeting Guide */}
      <div className="container-custom py-12">
        <SkinConcernsTargetingGuide />
      </div>

      {/* Market Intelligence */}
      <div className="section-padding bg-white">
        <div className="container-custom">
          <h2 className="text-3xl font-bold text-brand-charcoal mb-8 text-center">
            Global Market Intelligence
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-6 text-center">
              <h3 className="font-semibold text-brand-charcoal mb-2">🇺🇸 United States</h3>
              <p className="text-sm text-gray-600 mb-3">Leading in collagen, hyaluronic acid, and vitamin C searches</p>
              <div className="text-xs text-gray-500">Top trends: AI personalization, exosome therapy</div>
            </div>
            <div className="card p-6 text-center">
              <h3 className="font-semibold text-brand-charcoal mb-2">🇰🇷 South Korea</h3>
              <p className="text-sm text-gray-600 mb-3">Dominating in glass skin and K-beauty trends</p>
              <div className="text-xs text-gray-500">Top trends: Marine microalgae, exosome serums</div>
            </div>
            <div className="card p-6 text-center">
              <h3 className="font-semibold text-brand-charcoal mb-2">🇬🇧 United Kingdom</h3>
              <p className="text-sm text-gray-600 mb-3">Strong interest in bio-retinol and clean beauty</p>
              <div className="text-xs text-gray-500">Top trends: Sustainable ingredients, plant-based actives</div>
            </div>
            <div className="card p-6 text-center">
              <h3 className="font-semibold text-brand-charcoal mb-2">🇨🇦 Canada</h3>
              <p className="text-sm text-gray-600 mb-3">Growing interest in traditional and next-gen ingredients</p>
              <div className="text-xs text-gray-500">Top trends: Beef tallow, skin-gut axis products</div>
            </div>
          </div>
        </div>
      </div>

      {/* Next-Gen Innovation */}
      <div className="section-padding bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="container-custom">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-brand-charcoal mb-4">
              The Future of Skincare Innovation
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              Discover the next generation of skincare ingredients that are revolutionizing 
              the beauty industry with advanced technology and sustainable innovation.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <Sparkles className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-brand-charcoal mb-2">Bio-Technology</h3>
              <p className="text-gray-600 text-sm mb-3">
                Plant-derived alternatives and cellular communication technology
              </p>
              <div className="text-xs text-purple-600 font-medium">
                Bio-retinol, Exosome therapy, Marine microalgae
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-brand-charcoal mb-2">AI Personalization</h3>
              <p className="text-gray-600 text-sm mb-3">
                Custom formulations based on individual skin analysis
              </p>
              <div className="text-xs text-blue-600 font-medium">
                Algorithm-driven actives, Smart diagnostics
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-brand-charcoal mb-2">Holistic Health</h3>
              <p className="text-gray-600 text-sm mb-3">
                Inside-out approach targeting the skin-gut connection
              </p>
              <div className="text-xs text-green-600 font-medium">
                Microbiome skincare, Probiotic beauty
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngredientsPage;