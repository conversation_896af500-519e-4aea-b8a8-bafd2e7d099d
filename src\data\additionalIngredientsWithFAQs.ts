import { AdditionalIngredient } from './additionalIngredients';

// Enhanced interface with FAQs
export interface EnhancedIngredient extends AdditionalIngredient {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

// Convert existing ingredients to enhanced ingredients with FAQs
export const enhanceWithFAQs = (ingredient: AdditionalIngredient): EnhancedIngredient => {
  // Generate FAQs based on ingredient type
  let faqs: Array<{question: string, answer: string}> = [];
  
  // Common FAQs for all ingredients
  faqs.push({
    question: `Is ${ingredient.name} safe for all skin types?`,
    answer: `${ingredient.name} is generally ${ingredient.safetyProfile.generalSafety.toLowerCase()}. It's particularly suitable for ${ingredient.bestFor.join(', ').toLowerCase()}.`
  });
  
  // Type-specific FAQs
  if (ingredient.type === 'Humectant') {
    faqs.push(
      {
        question: `How does ${ingredient.name} hydrate the skin?`,
        answer: `As a humectant, ${ingredient.name} works by attracting water molecules from the environment and deeper skin layers to the surface, providing hydration. ${ingredient.description}`
      },
      {
        question: `Should I apply ${ingredient.name} to dry or damp skin?`,
        answer: `For best results, apply products containing ${ingredient.name} to slightly damp skin. This gives the humectant water molecules to bind to, maximizing its hydrating effects.`
      }
    );
  }
  
  if (ingredient.type === 'Emollient') {
    faqs.push(
      {
        question: `Will ${ingredient.name} make my skin feel greasy?`,
        answer: `${ingredient.name} is an emollient that softens and smooths the skin by filling in gaps between skin cells. The texture and feel depend on the specific formulation, but it's designed to absorb well without excessive greasiness.`
      },
      {
        question: `Is ${ingredient.name} comedogenic?`,
        answer: `${ingredient.name} has a ${ingredient.name.includes('Jojoba') || ingredient.name.includes('Squalane') ? 'low' : 'moderate'} risk of clogging pores. It's particularly suitable for ${ingredient.bestFor.join(', ').toLowerCase()}.`
      }
    );
  }
  
  if (ingredient.type === 'Occlusive') {
    faqs.push(
      {
        question: `How does ${ingredient.name} differ from humectants?`,
        answer: `While humectants attract water to the skin, ${ingredient.name} is an occlusive that forms a protective barrier on the skin's surface to prevent water loss. It's particularly effective for sealing in moisture after applying hydrating products.`
      },
      {
        question: `When should I use ${ingredient.name} in my routine?`,
        answer: `${ingredient.name} works best as one of the final steps in your skincare routine to seal in moisture and other treatments. Apply it after water-based products like serums and moisturizers.`
      }
    );
  }
  
  if (ingredient.type === 'Retinoid') {
    faqs.push(
      {
        question: `How often should I use ${ingredient.name}?`,
        answer: `When starting with ${ingredient.name}, begin with 1-2 times per week and gradually increase frequency as your skin builds tolerance. Most people can eventually use it 3-5 times weekly for optimal results.`
      },
      {
        question: `Will ${ingredient.name} cause irritation?`,
        answer: `${ingredient.name} has a ${ingredient.safetyProfile.sensitivityRisk.toLowerCase()} of causing irritation. Initial dryness, redness, or peeling is common as your skin adjusts. Start slowly and use a moisturizer to minimize these effects.`
      },
      {
        question: `Can I use ${ingredient.name} during pregnancy?`,
        answer: `${ingredient.safetyProfile.pregnancySafety}. Always consult with your healthcare provider before using any retinoid products during pregnancy or breastfeeding.`
      }
    );
  }
  
  if (ingredient.type === 'Alpha-Hydroxy Acid (AHA)' || ingredient.type === 'Beta-Hydroxy Acid (BHA)' || ingredient.type === 'Poly-Hydroxy Acid (PHA)') {
    faqs.push(
      {
        question: `How often should I exfoliate with ${ingredient.name}?`,
        answer: `For most skin types, using ${ingredient.name} 2-3 times per week is sufficient. Over-exfoliation can damage your skin barrier, so it's important not to use it daily unless specifically formulated for daily use at a lower concentration.`
      },
      {
        question: `Should I use sunscreen when using ${ingredient.name}?`,
        answer: `Yes, absolutely. ${ingredient.name} can increase your skin's sensitivity to the sun. Daily use of broad-spectrum SPF 30+ is essential when incorporating any exfoliating acid into your routine.`
      }
    );
  }
  
  if (ingredient.type === 'Antioxidant') {
    faqs.push(
      {
        question: `When is the best time to apply ${ingredient.name}?`,
        answer: `${ingredient.name} is most effective when applied in the morning to help protect your skin from environmental damage throughout the day. However, it can also be beneficial when used in evening routines.`
      },
      {
        question: `How does ${ingredient.name} protect the skin?`,
        answer: `As an antioxidant, ${ingredient.name} helps neutralize free radicals that can damage skin cells and accelerate aging. It works by donating electrons to unstable free radical molecules, preventing them from causing oxidative stress.`
      }
    );
  }
  
  if (ingredient.type === 'Brightening Agent' || ingredient.name.includes('Kojic') || ingredient.name.includes('Arbutin')) {
    faqs.push(
      {
        question: `How long does it take to see results with ${ingredient.name}?`,
        answer: `Most people begin to see initial improvements in skin tone and hyperpigmentation after 4-8 weeks of consistent use of ${ingredient.name}. More significant results typically become visible after 12 weeks.`
      },
      {
        question: `Can ${ingredient.name} be used with vitamin C?`,
        answer: `Yes, ${ingredient.name} can generally be used with vitamin C for enhanced brightening effects. The combination can be particularly effective for addressing hyperpigmentation and uneven skin tone.`
      }
    );
  }
  
  // Add more type-specific FAQs as needed
  
  // Ensure we have at least 3 FAQs
  if (faqs.length < 3) {
    faqs.push({
      question: `What are the main benefits of ${ingredient.name}?`,
      answer: `${ingredient.name} offers several key benefits including ${ingredient.benefits.join(', ')}. It's particularly effective for ${ingredient.bestFor.slice(0, 2).join(' and ')}.`
    });
  }
  
  return {
    ...ingredient,
    faqs: faqs
  };
};