import React from 'react';
import { Clock, <PERSON>, Sparkles, Sun, Zap, Droplets } from 'lucide-react';

const SkincareByAgeGuide: React.FC = () => {
  const ageGroups = [
    {
      age: '20s',
      focus: 'Prevention & Protection',
      icon: Shield,
      color: 'bg-blue-500',
      textColor: 'text-blue-800',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      keyIngredients: [
        { name: 'Antioxidants', why: 'Prevent free radical damage' },
        { name: 'Sunscreen', why: 'Prevent premature aging' },
        { name: 'Gentle Exfoliants', why: 'Maintain skin clarity' },
        { name: 'Hydrators', why: 'Maintain moisture balance' },
      ],
      routine: [
        'Gentle cleanser',
        'Vitamin C serum (AM)',
        'Lightweight moisturizer',
        'SPF 30+ (AM)',
        'Exfoliate 1-2x weekly',
      ],
      avoidables: [
        'Heavy anti-aging products',
        'Harsh physical scrubs',
        'Overly rich moisturizers',
      ],
    },
    {
      age: '30s',
      focus: 'Early Intervention',
      icon: Zap,
      color: 'bg-purple-500',
      textColor: 'text-purple-800',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      keyIngredients: [
        { name: 'Retinol', why: 'Begin collagen stimulation' },
        { name: 'Peptides', why: 'Support skin structure' },
        { name: 'Niacinamide', why: 'Multi-benefit for aging concerns' },
        { name: 'Hyaluronic Acid', why: 'Maintain hydration levels' },
      ],
      routine: [
        'Gentle cleanser',
        'Antioxidant serum (AM)',
        'Hydrating serum',
        'Retinol 2-3x weekly (PM)',
        'Richer moisturizer',
        'SPF 30-50 (AM)',
      ],
      avoidables: [
        'Skipping sunscreen',
        'Neglecting the neck area',
        'Inconsistent routines',
      ],
    },
    {
      age: '40s',
      focus: 'Targeted Treatment',
      icon: Target,
      color: 'bg-teal-500',
      textColor: 'text-teal-800',
      bgColor: 'bg-teal-50',
      borderColor: 'border-teal-200',
      keyIngredients: [
        { name: 'Higher % Retinoids', why: 'Accelerate cell turnover' },
        { name: 'Peptide Complexes', why: 'Boost collagen production' },
        { name: 'AHAs', why: 'Improve texture and tone' },
        { name: 'Antioxidant Blends', why: 'Comprehensive protection' },
      ],
      routine: [
        'Cream or oil cleanser',
        'Antioxidant complex (AM)',
        'Hydrating layers',
        'Retinoid 3-5x weekly (PM)',
        'Rich moisturizer',
        'Eye cream',
        'SPF 50 (AM)',
      ],
      avoidables: [
        'Harsh, stripping products',
        'Ignoring the neck/chest',
        'Overexfoliation',
      ],
    },
    {
      age: '50s+',
      focus: 'Restoration & Nourishment',
      icon: Sparkles,
      color: 'bg-amber-500',
      textColor: 'text-amber-800',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200',
      keyIngredients: [
        { name: 'Growth Factors', why: 'Skin regeneration' },
        { name: 'Ceramides', why: 'Strengthen barrier function' },
        { name: 'Prescription Retinoids', why: 'Maximum collagen support' },
        { name: 'Rich Oils', why: 'Replenish lipid content' },
      ],
      routine: [
        'Nourishing oil cleanser',
        'Hydrating toner',
        'Multiple serum layers',
        'Growth factor treatments',
        'Rich moisturizer + facial oil',
        'Targeted treatments',
        'SPF 50+ (AM)',
      ],
      avoidables: [
        'Foaming cleansers',
        'Alcohol-based products',
        'Skipping hydration',
      ],
    },
  ];

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-teal/10 rounded-full mb-4">
          <Clock className="w-8 h-8 text-brand-teal" />
        </div>
        <h2 className="text-2xl font-bold text-brand-charcoal mb-2">
          Skincare Through the Decades
        </h2>
        <p className="text-gray-600">
          How to adapt your skincare routine as you age
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {ageGroups.map((group, index) => (
          <div 
            key={index} 
            className={`rounded-xl border ${group.borderColor} ${group.bgColor} overflow-hidden animate-slide-up`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`${group.color} p-4 text-white`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <group.icon className="w-6 h-6" />
                  <h3 className="text-lg font-bold">In Your {group.age}</h3>
                </div>
                <span className="text-sm bg-white/20 px-3 py-1 rounded-full">
                  {group.focus}
                </span>
              </div>
            </div>
            
            <div className="p-4">
              <div className="mb-4">
                <h4 className={`text-sm font-medium ${group.textColor} mb-2`}>Key Ingredients:</h4>
                <ul className="space-y-2">
                  {group.keyIngredients.map((ingredient, idx) => (
                    <li key={idx} className="flex items-start space-x-2">
                      <div className={`w-2 h-2 ${group.color} rounded-full mt-2 flex-shrink-0`}></div>
                      <div>
                        <span className="text-sm font-medium text-gray-700">{ingredient.name}</span>
                        <p className="text-xs text-gray-500">{ingredient.why}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="mb-4">
                <h4 className={`text-sm font-medium ${group.textColor} mb-2`}>Ideal Routine:</h4>
                <div className="bg-white rounded-lg p-3 border border-gray-100">
                  <ul className="text-xs text-gray-600 space-y-1">
                    {group.routine.map((step, idx) => (
                      <li key={idx} className="flex items-center space-x-2">
                        <span className="text-xs bg-gray-100 w-5 h-5 rounded-full flex items-center justify-center font-medium text-gray-700">{idx + 1}</span>
                        <span>{step}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div>
                <h4 className={`text-sm font-medium ${group.textColor} mb-2`}>What to Avoid:</h4>
                <ul className="text-xs text-gray-600 space-y-1">
                  {group.avoidables.map((item, idx) => (
                    <li key={idx} className="flex items-start space-x-1">
                      <span className="text-red-500">✕</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h3 className="font-semibold text-brand-charcoal mb-3">Universal Skincare Truths (All Ages)</h3>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Sun className="w-4 h-4 text-yellow-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-800 text-sm">Sun Protection</h4>
              <p className="text-xs text-gray-600">Daily SPF is non-negotiable at every age</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Droplets className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-800 text-sm">Hydration</h4>
              <p className="text-xs text-gray-600">Well-hydrated skin ages better at any age</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Sparkles className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-800 text-sm">Consistency</h4>
              <p className="text-xs text-gray-600">Regular care outperforms occasional treatments</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Add missing Target icon
const Target = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <circle cx="12" cy="12" r="6" />
      <circle cx="12" cy="12" r="2" />
    </svg>
  );
};

export default SkincareByAgeGuide;